import { useState, useRef, useEffect } from "react";
import { X, Calendar, ChevronDown } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import type { DateRange } from "@/components/common/Datepicker/DatePicker";

export interface ReviewsFilterData {
	locations: string[];
	providers: string[];
	services: string[];
	categories: string[];
	dateRange: DateRange | undefined;
}

interface ReviewsFilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters: (filters: ReviewsFilterData) => void;
	onResetFilters: () => void;
}

// Mock data - in real implementation, these would come from APIs
const mockLocations = [
	{ id: "1", name: "Location name 1" },
	{ id: "2", name: "Location name 2" },
	{ id: "3", name: "Location name 3" },
	{ id: "4", name: "Downtown Clinic" },
	{ id: "5", name: "Uptown Medical Center" },
];

const mockProviders = [
	{ id: "1", name: "Dr. John Lee" },
	{ id: "2", name: "Dr. Cunter Halls" },
	{ id: "3", name: "Dr. <PERSON> Cohen" },
	{ id: "4", name: "Dr. Sarah Wilson" },
	{ id: "5", name: "Dr. <PERSON> Brown" },
];

const mockServices = [
	{ id: "1", name: "Blood Work" },
	{ id: "2", name: "Immunization" },
	{ id: "3", name: "Lab Tests" },
	{ id: "4", name: "Consultation" },
	{ id: "5", name: "Physical Exam" },
];

const mockCategories = [
	{ id: "1", name: "All" },
	{ id: "2", name: "Excellent" },
	{ id: "3", name: "Good" },
	{ id: "4", name: "Average" },
	{ id: "5", name: "Poor" },
];

interface MultiSelectFieldProps {
	label: string;
	selectedItems: string[];
	onItemsChange: (items: string[]) => void;
	options: { id: string; name: string }[];
	placeholder?: string;
}

function MultiSelectField({
	label,
	selectedItems,
	onItemsChange,
	options,
	placeholder = "Select options",
}: MultiSelectFieldProps) {
	const [isOpen, setIsOpen] = useState(false);
	const dropdownRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setIsOpen(false);
			}
		};

		if (isOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen]);

	const handleItemToggle = (itemId: string) => {
		if (selectedItems.includes(itemId)) {
			onItemsChange(selectedItems.filter((id) => id !== itemId));
		} else {
			onItemsChange([...selectedItems, itemId]);
		}
	};

	const handleItemRemove = (itemId: string) => {
		onItemsChange(selectedItems.filter((id) => id !== itemId));
	};

	const getSelectedNames = () => {
		return selectedItems
			.map((id) => options.find((opt) => opt.id === id)?.name)
			.filter(Boolean);
	};

	const handleClearAll = () => {
		onItemsChange([]);
	};

	return (
		<div
			ref={dropdownRef}
			className="relative flex flex-col items-start justify-start gap-2"
		>
			<Label className="text-base-foreground text-xs leading-3 font-medium">
				{label}
			</Label>
			<div className="flex flex-col items-start justify-start gap-2.5 self-stretch">
				<button
					onClick={() => setIsOpen(!isOpen)}
					className="inline-flex h-9 items-center justify-start gap-1 self-stretch overflow-hidden rounded-md bg-white px-3 py-2 outline outline-1 outline-offset-[-1px] outline-gray-300 hover:bg-gray-50"
				>
					<div className="flex flex-1 flex-wrap content-center items-center justify-start gap-1">
						{getSelectedNames().map((name, index) => {
							const itemId = selectedItems[index];
							return (
								<Badge
									key={index}
									variant="outline"
									className="flex items-center justify-center gap-1 rounded-md bg-sky-800/10 px-2 py-1 outline outline-[0.50px] outline-offset-[-0.50px] outline-sky-800"
								>
									<span className="text-[10px] leading-3 font-medium text-gray-900">
										{name}
									</span>
									<button
										onClick={(e) => {
											e.stopPropagation();
											if (itemId)
												handleItemRemove(itemId);
										}}
										className="relative h-3 w-3 overflow-hidden rounded hover:bg-sky-900/20"
									>
										<X className="absolute top-[2.50px] left-[2.50px] h-1.5 w-1.5 text-gray-900" />
									</button>
								</Badge>
							);
						})}
					</div>
					<div className="flex h-5 items-center justify-start gap-1">
						{selectedItems.length > 0 && (
							<button
								onClick={(e) => {
									e.stopPropagation();
									handleClearAll();
								}}
								className="relative h-4 w-4 overflow-hidden rounded-sm opacity-70 hover:opacity-100"
							>
								<X className="absolute top-[3.33px] left-[3.33px] h-2.5 w-2.5 text-gray-900" />
							</button>
						)}
						<div className="h-0 w-3 origin-top-left rotate-90 outline outline-1 outline-offset-[-0.50px] outline-gray-300"></div>
						<ChevronDown className="h-4 w-4 text-gray-900" />
					</div>
				</button>
				{isOpen && (
					<div className="absolute top-full right-0 left-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
						{options.map((option) => (
							<div
								key={option.id}
								className="flex cursor-pointer items-center gap-2 px-3 py-2 hover:bg-gray-100"
								onClick={() => handleItemToggle(option.id)}
							>
								<input
									type="checkbox"
									checked={selectedItems.includes(option.id)}
									onChange={() => handleItemToggle(option.id)}
									className="h-4 w-4"
								/>
								<span className="text-sm">{option.name}</span>
							</div>
						))}
					</div>
				)}
			</div>
		</div>
	);
}

export function ReviewsFilterSheet({
	open,
	onOpenChange,
	onApplyFilters,
	onResetFilters,
}: ReviewsFilterSheetProps) {
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>([
		"1",
		"2",
		"3",
	]);
	const [selectedCategories, setSelectedCategories] = useState<string[]>([
		"1",
	]);
	const [dateRange, setDateRange] = useState<DateRange | undefined>(
		undefined
	);

	const handleApply = () => {
		onApplyFilters({
			locations: selectedLocations,
			providers: selectedProviders,
			services: selectedServices,
			categories: selectedCategories,
			dateRange,
		});
		onOpenChange(false);
	};

	const handleReset = () => {
		setSelectedLocations([]);
		setSelectedProviders([]);
		setSelectedServices([]);
		setSelectedCategories([]);
		setDateRange(undefined);
		onResetFilters();
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="inline-flex h-full w-[525px] flex-col items-end justify-start gap-6 overflow-hidden bg-white p-9 shadow-[0px_4px_6px_-2px_rgba(0,0,0,0.05)] shadow-lg [&>button]:hidden">
				<SheetHeader className="flex w-[453px] flex-col items-start justify-start gap-4 p-0">
					<div className="inline-flex items-center justify-start gap-24 self-stretch">
						<SheetTitle className="text-base-card-foreground w-80 justify-start font-['Inter'] text-2xl leading-normal font-semibold">
							Filter Reviews
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="flex items-center justify-start gap-2.5 p-2"
						>
							<X className="h-3.5 w-3.5 text-gray-400" />
						</Button>
					</div>
					<p className="text-base-muted-foreground justify-start self-stretch font-['Inter'] text-base leading-tight font-normal">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="flex flex-1 flex-col items-start justify-start gap-4 self-stretch">
					<div className="flex w-[453px] flex-col items-start justify-center gap-6">
						<MultiSelectField
							label="Locations"
							selectedItems={selectedLocations}
							onItemsChange={setSelectedLocations}
							options={mockLocations}
						/>

						<MultiSelectField
							label="Providers"
							selectedItems={selectedProviders}
							onItemsChange={setSelectedProviders}
							options={mockProviders}
						/>

						<MultiSelectField
							label="Services"
							selectedItems={selectedServices}
							onItemsChange={setSelectedServices}
							options={mockServices}
						/>

						<MultiSelectField
							label="Categories"
							selectedItems={selectedCategories}
							onItemsChange={setSelectedCategories}
							options={mockCategories}
						/>

						<div className="flex flex-col items-start justify-start gap-2 self-stretch">
							<Label className="text-xs leading-3 font-medium text-gray-900">
								Select a Date or Range
							</Label>
							<DatePicker
								variant="range"
								value={dateRange}
								onChange={(range) =>
									setDateRange(range as DateRange)
								}
								placeholder="Pick a date"
								className="h-9 self-stretch"
							/>
						</div>
					</div>
				</div>

				<SheetFooter className="inline-flex items-start justify-between self-stretch p-0">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="h-auto p-0 text-sm leading-none font-normal text-gray-500"
					>
						Reset
					</Button>
					<div className="flex w-72 items-center justify-end gap-2.5">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
							className="h-10 w-20 rounded-md bg-white px-4 py-2 outline outline-1 outline-offset-[-1px] outline-gray-300"
						>
							<span className="text-base-foreground text-sm leading-tight font-medium">
								Cancel
							</span>
						</Button>
						<Button
							onClick={handleApply}
							className="h-10 rounded-md bg-sky-800 px-5 py-2"
						>
							<span className="text-sm leading-tight font-medium text-white">
								Apply
							</span>
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
